*   ### **技术产品需求文档：自动模板填充工具**

    **文档目的**: 本文档为开发团队提供“自动模板填充工具”的功能与技术实现规格。所有需求均以可执行、可测试为标准进行描述。

    ---

    ### **1. 核心概念定义 (Core Concepts)**

    为确保术语统一，定义以下核心实体：
    *   **项目 (Project)**: 一个独立的任务配置单元，封装了**数据源路径、模板路径、字段映射关系、输出配置**。以 `.json` 格式持久化。
    *   **数据源 (Data Source)**: 指向一个外部的 **CSV** 或 **XLSX** 文件。系统从中读取结构化数据。
    *   **模板 (Template)**: 一个包含占位符的 **DOCX** 文件。
    *   **占位符 (Placeholder)**: 模板中定义的、格式为 `{FieldName}` 的待替换标记。
    *   **字段映射 (Field Mapping)**: 定义数据源中的列（Header）与模板中占位符之间的对应关系。

    ---

    ### **2. 功能规格 (Functional Specifications)**

    #### **模块 1: 项目管理 (M1: Project Management)**

    | ID       | 功能点       | 优先级 | 规格描述与验收标准                                           |
    | :------- | :----------- | :----- | :----------------------------------------------------------- |
    | **M1.1** | 项目生命周期 | **P0** | 1. **创建**: 初始化一个空的配置对象。<br>2. **保存/另存为**: 将当前配置（数据源/模板路径、映射关系、输出规则）序列化为 `.json` 文件并写入磁盘。<br>3. **加载**: 从 `.json` 文件反序列化配置，并恢复UI状态。加载后，系统需验证文件路径有效性。<br>4. **删除**: 从项目列表和文件系统中移除项目。 |
    | **M1.2** | 项目列表     | **P0** | 1. 应用启动时，在UI左侧固定区域展示最近使用的项目列表。<br>2. 列表项应显示项目名称，点击即可触发加载（M1.1）操作。 |
    | **M1.3** | 状态自动保存 | **P0** | 1. 用户在项目内进行的任何配置更改（如修改映射、调整输出路径）都应被实时记录在内存中的项目状态对象中。<br>2. 关闭应用前若有未保存的更改，应提示用户保存。 |

    #### **模块 2: 数据处理 (M2: Data Processing)**

    | ID       | 功能点     | 优先级 | 规格描述与验收标准                                           |
    | :------- | :--------- | :----- | :----------------------------------------------------------- |
    | **M2.1** | 数据源导入 | **P0** | 1. **支持格式**: 必须支持 **CSV** 和 **XLSX**。<br>2. **编码处理**: 必须能正确解析 **UTF-8** 和 **GBK** 编码的CSV文件，无需用户干预。<br>3. **XLSX工作表**: 导入XLSX时，若文件包含多个工作表(Sheet)，必须弹窗让用户选择一个。默认选择第一个。<br>4. **表头提取**: 必须自动将所选数据源的首行作为列标题（Header）。 |
    | **M2.2** | 数据预处理 | **P0** | 1. **自动清洗**: 导入时，必须自动去除所有单元格数据首尾的空白字符（trim）。<br>2. **空行策略**: 提供配置项：“跳过空行”（默认）或“报告错误”。此配置随项目保存。 |
    | **M2.3** | 数据预览   | **P0** | 1. 在UI中提供一个表格组件，用于预览导入的数据。<br>2. 为优化性能，预览默认加载前100行数据。 |

    #### **模块 3: 模板与映射 (M3: Template & Mapping)**

    | ID       | 功能点         | 优先级 | 规格描述与验收标准                                           |
    | :------- | :------------- | :----- | :----------------------------------------------------------- |
    | **M3.1** | 模板加载与解析 | **P0** | 1. **支持格式**: 必须支持 **DOCX** 文件。<br>2. **占位符提取**: 加载模板后，必须解析整个文档，提取所有格式为 `{FieldName}` 的占位符列表。<br>3. **【关键】健壮性解析**: 解析逻辑必须能处理被Word内部XML格式化标签（如`<w:r>`, `<w:t>`）分割的占位符。例如，`{` `</w:t></w:r><w:r><w:t>` `FieldName` `</w:t></w:r><w:r><w:t>` `}` 这样的片段必须被正确识别为一个完整的占位符`{FieldName}`。 |
    | **M3.2** | 可视化字段映射 | **P0** | 1. **UI**: 提供一个双栏并列的UI，左侧为数据源的Headers，右侧为模板提取的Placeholders。<br>2. **自动匹配**: 系统加载数据和模板后，应执行一次自动匹配：忽略大小写和`_`/` `差异，将名称相同的字段预先关联。<br>3. **手动映射**: 用户必须能通过拖拽或下拉选择的方式，手动建立、修改或清除映射关系。<br>4. **未映射处理**: 为未映射的占位符提供处理策略选项：“保留原样”、“替换为空字符串”（默认）、“替换为指定文本”。此配置随项目保存。 |

    #### **模块 4: 输出引擎 (M4: Output Engine)**

    | ID       | 功能点       | 优先级 | 规格描述与验收标准                                           |
    | :------- | :----------- | :----- | :----------------------------------------------------------- |
    | **M4.1** | 动态输出配置 | **P0** | 1. **输出路径**: 用户可指定一个根目录。支持使用数据列变量创建动态子目录，语法为`{HeaderName}`。例：`./output/{部门}`。<br>2. **文件名**: 用户可自定义输出文件名，同样支持使用`{HeaderName}`变量。例：`{姓名}-劳动合同.docx`。<br>3. **文件名净化**: 在生成文件时，必须自动将最终文件名中的系统非法字符（`\ / : * ? " < > |`）替换为下划线 `_`。 |
    | **M4.2** | 批量生成     | **P0** | 1. 遍历数据源中的每一有效行（根据M2.2的空行策略）。<br>2. 对每一行，根据M3.2的映射规则和M4.1的输出配置，生成一份独立的、已填充内容的DOCX文档。<br>3. **事务性**: 单行生成失败（如数据格式问题）不得中断整个批量任务。失败信息需记录到日志。 |
    | **M4.3** | 任务监控     | **P0** | 1. **进度条**: UI必须提供一个明确的进度条，实时显示 `已完成/总数`。<br>2. **实时日志**: UI必须提供一个日志面板，滚动输出 `[时间戳] [级别] 消息`，如“正在处理第 X 行...”、“文件 Y.docx 生成成功”、“第 Z 行数据格式错误：...”。 |
    | **M4.4** | PDF输出增强  | **P1** | 1. **单文件转换**: 提供布尔选项，决定是否在生成每个DOCX的同时，调用转换服务生成对应的PDF副本。<br>2. **合并输出**: 提供布尔选项，决定是否在任务结束后，将本次生成的所有文档（DOCX或PDF）合并为一个单一的PDF文件。<br>3. **自动书签**: 若执行合并PDF，必须根据每个源文件的最终生成名，在合并后的PDF中创建层级书签，用于快速导航。 |

    ---

    ### **3. 非功能性需求 (Non-Functional Requirements)**

    | 类别       | 需求描述                                                     |
    | :--------- | :----------------------------------------------------------- |
    | **性能**   | - **大数据处理**: 在目标配置（i5/8GB RAM/SSD）下，处理1万行、20列的XLSX数据源，生成1万个DOCX文件的总耗时应小于5分钟。<br>- **UI响应**: 所有UI交互（点击、拖拽、输入）的视觉响应必须在100ms内，后台任务不得阻塞UI线程。 |
    | **可靠性** | - **格式保真**: 替换过程不得破坏DOCX原有的任何样式、页眉页脚、图片、表格布局等。<br>- **错误处理**: 对所有可预见的错误（如文件不存在、无权限、格式解析失败），必须向用户提供清晰、无技术术语的错误提示，并在日志中记录详细的技术原因。 |
    | **兼容性** | - **平台**: 必须交付可在 **Windows 10/11 (x64)** 和 **macOS (Intel & Apple Silicon)** 上独立运行的应用程序包。<br>- **环境**: 应用必须是自包含的，不要求用户预先安装任何第三方运行时（如.NET, Java, Python）。 |
    | **安全性** | - **本地执行**: 所有文件处理和数据操作必须且只能在用户本地计算机上进行。严禁任何数据通过网络上传。 |

    ---

    ### **4. 技术与架构要求 (Technical & Architectural Requirements)**

    #### **4.1 技术栈**
    *   **核心框架**: **Tauri 2.0** (Rust Backend + Webview Frontend)
    *   **前端实现**: **React** 或 **Vue 3** (使用 TypeScript/TSX)
    *   **后端核心库**:
        *   **异步运行时**: `tokio` (由Tauri集成)
        *   **Excel/CSV处理**: `calamine`, `csv`
        *   **DOCX处理**: `docx-rs`
        *   **PDF生成/转换**: 推荐使用 `headless_chrome` 库调用系统安装的Chrome/Edge，通过其打印到PDF的功能实现高质量转换。备选方案为 `genpdf`。

    #### **4.2 核心架构**
    *   **模型**: 前端-后端分离的混合应用模型。
        *   **前端 (View)**: 负责UI渲染、用户交互和应用状态管理。通过Tauri的 `invoke` API调用后端命令。
        *   **后端 (Core)**: 纯Rust逻辑，通过Tauri的 `#[tauri::command]` 宏暴露异步接口。负责所有文件I/O和计算密集型任务。
    *   **线程模型**: UI运行于主线程。所有后端命令必须在`tokio`线程池中**异步执行**，并通过`Promise/Future`将结果或错误返回给前端，**严防UI冻结**。
